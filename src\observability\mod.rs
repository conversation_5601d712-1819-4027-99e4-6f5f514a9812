use crate::config::Config;
use anyhow::Result;
// Commented out until opentelemetry SDK is properly configured
// use opentelemetry::{
//     global,
//     sdk::{
//         trace::{self, RandomIdGenerator, Sampler},
//         Resource,
//     },
//     KeyValue,
// };
use opentelemetry_jaeger::new_agent_pipeline;
use tracing::{info, Level};
use tracing_opentelemetry::OpenTelemetryLayer;
use tracing_subscriber::{
    fmt::{self, format::FmtSpan},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Layer,
};

pub async fn setup_tracing(config: &Config) -> Result<()> {
    // Create the tracing subscriber with basic formatting
    let env_filter = EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| EnvFilter::new(&config.log_level));

    let fmt_layer = fmt::layer()
        .with_target(true)
        .with_thread_ids(true)
        .with_span_events(FmtSpan::CLOSE)
        .with_filter(env_filter);

    // For development mode, just use basic tracing without Jaeger
    if let Some(jaeger_endpoint) = &config.jaeger_endpoint {
        if !jaeger_endpoint.is_empty() {
            info!("Setting up OpenTelemetry with Jaeger endpoint: {}", jaeger_endpoint);

            // Try to set up Jaeger, but fall back to basic tracing if it fails
            match new_agent_pipeline()
                .with_endpoint(jaeger_endpoint)
                .with_service_name("achidas-backend")
                .install_batch(opentelemetry_sdk::runtime::Tokio)
            {
                Ok(tracer) => {
                    let telemetry_layer = OpenTelemetryLayer::new(tracer);
                    tracing_subscriber::registry()
                        .with(fmt_layer)
                        .with(telemetry_layer)
                        .init();
                    info!("Tracing initialized with Jaeger support");
                },
                Err(e) => {
                    eprintln!("Failed to initialize Jaeger tracing: {}, falling back to basic tracing", e);
                    tracing_subscriber::registry()
                        .with(fmt_layer)
                        .init();
                    info!("Tracing initialized without Jaeger (fallback mode)");
                }
            }
        } else {
            info!("Jaeger endpoint is empty, using basic tracing");
            tracing_subscriber::registry()
                .with(fmt_layer)
                .init();
            info!("Tracing initialized without Jaeger");
        }
    } else {
        info!("No Jaeger endpoint configured, using basic tracing");
        tracing_subscriber::registry()
            .with(fmt_layer)
            .init();
        info!("Tracing initialized without Jaeger");
    }

    Ok(())
}

pub fn setup_metrics(config: &Config) -> Result<()> {
    use metrics_exporter_prometheus::PrometheusBuilder;
    
    let builder = PrometheusBuilder::new();
    let handle = builder
        .with_http_listener(config.prometheus_endpoint.parse::<std::net::SocketAddr>()?)
        .install()?;

    info!("Prometheus metrics server started on {}", config.prometheus_endpoint);
    
    // Register custom metrics
    // Commented out until metrics crate is properly configured
    // metrics::register_counter!("http_requests_total", "Total number of HTTP requests");
    // metrics::register_histogram!("http_request_duration_seconds", "HTTP request duration in seconds");
    // metrics::register_gauge!("active_connections", "Number of active connections");
    // metrics::register_counter!("vultr_api_calls_total", "Total number of Vultr API calls");
    // metrics::register_counter!("vultr_api_errors_total", "Total number of Vultr API errors");
    // metrics::register_gauge!("active_instances", "Number of active instances");
    // metrics::register_counter!("user_registrations_total", "Total number of user registrations");
    // metrics::register_counter!("user_logins_total", "Total number of user logins");

    Ok(())
}

// Middleware for recording HTTP metrics
pub async fn record_http_metrics(
    req: axum::http::Request<axum::body::Body>,
    next: axum::middleware::Next,
) -> axum::response::Response {
    let start = std::time::Instant::now();
    let method = req.method().clone();
    let path = req.uri().path().to_string();

    let response = next.run(req).await;

    let duration = start.elapsed();
    let status = response.status().as_u16();

    // Record metrics - commented out until metrics crate is properly configured
    // metrics::increment_counter!("http_requests_total",
    //     "method" => method.to_string(),
    //     "path" => path.clone(),
    //     "status" => status.to_string()
    // );

    // metrics::histogram!("http_request_duration_seconds", duration.as_secs_f64(),
    //     "method" => method.to_string(),
    //     "path" => path,
    //     "status" => status.to_string()
    // );

    response
}

// Helper macros for common metrics
#[macro_export]
macro_rules! record_vultr_api_call {
    ($endpoint:expr) => {
        metrics::increment_counter!("vultr_api_calls_total", "endpoint" => $endpoint);
    };
}

#[macro_export]
macro_rules! record_vultr_api_error {
    ($endpoint:expr, $error:expr) => {
        metrics::increment_counter!("vultr_api_errors_total", 
            "endpoint" => $endpoint,
            "error" => $error
        );
    };
}

#[macro_export]
macro_rules! record_user_action {
    ($action:expr) => {
        metrics::increment_counter!(concat!("user_", $action, "_total"));
    };
}

pub use record_vultr_api_call;
pub use record_vultr_api_error;
pub use record_user_action;
