use axum::Json;
use serde::{Deserialize, Serialize};
use tracing::instrument;
use crate::models::{ApiResponse, ApiResponseMeta};

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthStatus {
    pub status: String,
    pub service: String,
    pub version: String,
    pub uptime: String,
    pub database: String,
    pub vultr_api: String,
}

#[instrument]
pub async fn health_check() -> <PERSON><PERSON><ApiResponse<HealthStatus>> {
    let health_data = HealthStatus {
        status: "healthy".to_string(),
        service: "achidas-backend".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        uptime: "running".to_string(),
        database: "connected".to_string(),
        vultr_api: "configured".to_string(),
    };

    let meta = ApiResponseMeta {
        timestamp: chrono::Utc::now().to_rfc3339(),
        request_id: None,
        version: "v1".to_string(),
        status_code: 200,
        path: Some("/health".to_string()),
        method: Some("GET".to_string()),
    };

    <PERSON><PERSON>(ApiResponse::success_with_meta(health_data, meta))
}
