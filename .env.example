# Achidas Backend Environment Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================
SERVER_ADDRESS=0.0.0.0:3000
HOST=127.0.0.1
PORT=8080
RUST_LOG=info
RUST_BACKTRACE=1

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Complete MongoDB connection string - replace with your actual connection string
# Examples:
# - Local: mongodb://localhost:27017/achidas
# - Atlas: mongodb+srv://username:<EMAIL>/achidas
# - Replica Set: ******************************************************************
DATABASE_URL=mongodb://localhost:27017/achidas

# =============================================================================
# JWT AUTHENTICATION
# =============================================================================
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRATION=3600
REFRESH_TOKEN_SECRET=your-super-secret-refresh-token-key-change-this
REFRESH_TOKEN_EXPIRATION=604800

# =============================================================================
# VULTR API CONFIGURATION
# =============================================================================
VULTR_API_KEY=your_vultr_api_key_here
VULTR_API_BASE_URL=https://api.vultr.com/v2

# Vultr API Rate Limiting
VULTR_RATE_LIMIT_REQUESTS_PER_SECOND=10
VULTR_RATE_LIMIT_BURST=20
VULTR_REQUEST_TIMEOUT=30

# Vultr Retry Configuration
VULTR_MAX_RETRIES=3
VULTR_RETRY_DELAY=1000
VULTR_RETRY_BACKOFF_MULTIPLIER=2

# Vultr Cache Configuration
VULTR_CACHE_TTL=300
VULTR_CACHE_ENABLED=true

# =============================================================================
# OBSERVABILITY CONFIGURATION
# =============================================================================
JAEGER_ENDPOINT=http://localhost:14268/api/traces
PROMETHEUS_ENDPOINT=0.0.0.0:9090
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_PATH=logs/achidas.log

# =============================================================================
# ENVIRONMENT
# =============================================================================
ENVIRONMENT=development
DEVELOPMENT_MODE=true
DEBUG_ENABLED=true
PRODUCTION_MODE=false

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With
CORS_ALLOW_CREDENTIALS=true

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=10

# =============================================================================
# ENCRYPTION & SECURITY
# =============================================================================
ENCRYPTION_KEY=your-32-character-encryption-key-here
BCRYPT_COST=12
SESSION_SECRET=your-session-secret-key-change-this

# =============================================================================
# REDIS CONFIGURATION (Optional - for caching)
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0

# =============================================================================
# METRICS & MONITORING
# =============================================================================
METRICS_ENABLED=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# =============================================================================
# BILLING & COST TRACKING
# =============================================================================
BILLING_CURRENCY=USD
BILLING_TAX_RATE=0.0
COST_TRACKING_ENABLED=true
INVOICE_GENERATION_ENABLED=true

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_INTERVAL=24h
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/var/backups/achidas

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================
WEBHOOK_SECRET=your-webhook-secret-key
WEBHOOK_TIMEOUT=30
WEBHOOK_RETRY_ATTEMPTS=3

# =============================================================================
# FEATURE FLAGS - VULTR API INTEGRATIONS
# =============================================================================
FEATURE_BARE_METAL_ENABLED=true
FEATURE_BLOCK_STORAGE_ENABLED=true
FEATURE_CDN_ENABLED=true
FEATURE_CONTAINER_REGISTRY_ENABLED=true
FEATURE_DNS_ENABLED=true
FEATURE_FIREWALL_ENABLED=true
FEATURE_INSTANCES_ENABLED=true
FEATURE_ISO_ENABLED=true
FEATURE_KUBERNETES_ENABLED=true
FEATURE_LOAD_BALANCER_ENABLED=true
FEATURE_DATABASES_ENABLED=true
FEATURE_MARKETPLACE_ENABLED=true
FEATURE_OBJECT_STORAGE_ENABLED=true
FEATURE_SERVERLESS_ENABLED=true
FEATURE_VPC_ENABLED=true
FEATURE_RESERVED_IP_ENABLED=true
FEATURE_SNAPSHOTS_ENABLED=true
FEATURE_SSH_KEYS_ENABLED=true
FEATURE_STARTUP_SCRIPTS_ENABLED=true
FEATURE_STORAGE_GATEWAYS_ENABLED=true
FEATURE_USER_MANAGEMENT_ENABLED=true
FEATURE_VFS_ENABLED=true

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
CACHE_ENABLED=true
CACHE_DEFAULT_TTL=300
CACHE_MAX_SIZE=1000
CACHE_CLEANUP_INTERVAL=3600

# =============================================================================
# WORKER CONFIGURATION
# =============================================================================
WORKER_THREADS=4
WORKER_QUEUE_SIZE=1000
WORKER_TIMEOUT=300

# =============================================================================
# API VERSIONING
# =============================================================================
API_VERSION=v1
API_PREFIX=/api
DEPRECATED_API_WARNING=true

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Achidas

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_ENABLED=false
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem

# =============================================================================
# EXTERNAL INTEGRATIONS
# =============================================================================
SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=
TELEGRAM_BOT_TOKEN=
TELEGRAM_CHAT_ID=

# =============================================================================
# MAINTENANCE MODE
# =============================================================================
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="System is under maintenance. Please try again later."
MAINTENANCE_ALLOWED_IPS=127.0.0.1,::1

# =============================================================================
# TIMEZONE & LOCALIZATION
# =============================================================================
TIMEZONE=UTC
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,es,fr,de,it,pt,ru,zh,ja,ko

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=10MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,txt,csv,json,xml
UPLOAD_PATH=/tmp/uploads

# =============================================================================
# TELEMETRY & ANALYTICS
# =============================================================================
TELEMETRY_ENABLED=false
ANALYTICS_ENABLED=false
USAGE_TRACKING_ENABLED=false
