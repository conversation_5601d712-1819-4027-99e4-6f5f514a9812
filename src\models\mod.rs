use bson::oid::ObjectId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::Validate;

pub mod user;
pub mod instance;
pub mod billing;

pub use user::*;
pub use instance::*;
pub use billing::*;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponseMeta {
    pub timestamp: String,
    pub request_id: Option<String>,
    pub version: String,
    pub status_code: u16,
    pub path: Option<String>,
    pub method: Option<String>,
}

impl Default for ApiResponseMeta {
    fn default() -> Self {
        Self {
            timestamp: chrono::Utc::now().to_rfc3339(),
            request_id: None,
            version: "v1".to_string(),
            status_code: 200,
            path: None,
            method: None,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
    pub error: Option<String>,
    pub meta: ApiResponseMeta,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
            meta: ApiResponseMeta::default(),
        }
    }

    pub fn success_with_meta(data: T, meta: ApiResponseMeta) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
            meta,
        }
    }

    pub fn error(error: String) -> Self {
        let mut meta = ApiResponseMeta::default();
        meta.status_code = 400;
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            meta,
        }
    }

    pub fn error_with_status(error: String, status_code: u16) -> Self {
        let mut meta = ApiResponseMeta::default();
        meta.status_code = status_code;
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            meta,
        }
    }

    pub fn error_with_meta(error: String, meta: ApiResponseMeta) -> Self {
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
            meta,
        }
    }

    pub fn message(message: String) -> Self {
        Self {
            success: true,
            data: None,
            message: Some(message),
            error: None,
            meta: ApiResponseMeta::default(),
        }
    }

    pub fn message_with_meta(message: String, meta: ApiResponseMeta) -> Self {
        Self {
            success: true,
            data: None,
            message: Some(message),
            error: None,
            meta,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationMeta {
    pub page: u32,
    pub per_page: u32,
    pub total: u64,
    pub total_pages: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub meta: PaginationMeta,
}

#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PaginationQuery {
    #[validate(range(min = 1, max = 100))]
    pub page: Option<u32>,
    #[validate(range(min = 1, max = 100))]
    pub per_page: Option<u32>,
}

impl Default for PaginationQuery {
    fn default() -> Self {
        Self {
            page: Some(1),
            per_page: Some(20),
        }
    }
}
